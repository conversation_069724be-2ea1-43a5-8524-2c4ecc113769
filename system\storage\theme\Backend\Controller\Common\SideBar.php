<?php

namespace Theme25\Backend\Controller\Common;

class SideBar extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/sidebar');
    }

    public function index() {

        // Проверка дали потребителят е логнат
        if (!$this->isUserLogged()) {
            return $this->loadView('common/sidebar', $this->data);
        }

        // Подготовка на данните с верижно извикване на методи
        $this->prepareLogoData()
             ->prepareMenuItems();

        // Рендиране на шаблона с данните от $this->data
        return $this->loadView('common/sidebar', $this->data);
    }

    /**
     * Подготвя данните за логото
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareLogoData() {
        $this->setData([
            'logo' => $this->getConfig('config_logo'),
            'name' => $this->getConfig('config_name'),
            'home' => $this->getAdminLink('common/dashboard')
        ]);

        return $this;
    }

    /**
     * Подготвя елементите на менюто
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareMenuItems() {
        $menus = [];

        // Добавяне на основните менюта
        $this->addDashboardMenu($menus)
             ->addProductsMenu($menus)
             ->addCategoriesMenu($menus)
             ->addCustomersMenu($menus)
             ->addOrdersMenu($menus)
             ->addRequestsMenu($menus)
             ->addSettingsMenu($menus)
             ->addReportsMenu($menus)
             ->addMarketingMenu($menus)
             ->addThemeMenu($menus);

        // Добавяне на менютата към данните
        $this->setData('menus', $menus);

        return $this;
    }

    /**
     * Добавя менюто за таблото
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addDashboardMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'common/dashboard')) {
            $menus[] = [
                'id'       => 'menu-dashboard',
                'icon'     => 'ri-dashboard-line',
                'name'     => 'Табло',
                'href'     => $this->getAdminLink('common/dashboard'),
                'active'   => $this->isActiveMainRoute('common/dashboard')
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за продукти
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addProductsMenu(&$menus) {
        // Създаване на масив за субменютата
        $products_submenu = [];

        // Добавяне на субменютата с проверка за права на достъп
        if ($this->hasPermission('access', 'catalog/product')) {
            $products_submenu[] = [
                'name'     => 'Продукти',
                'href'     => $this->getAdminLink('catalog/product'),
                'active'   => $this->isActiveRoute('catalog/product')
            ];
        }

        // Импорт - само ако съществува контролера
        if ($this->hasPermission('access', 'catalog/product/import')) {
            $products_submenu[] = [
                'name'     => 'Импорт',
                'href'     => $this->getAdminLink('catalog/product/import'),
                'active'   => $this->isActiveRoute('catalog/product/import')
            ];
        }

        // Експорт - само ако съществува контролера
        if ($this->hasPermission('access', 'catalog/product/export')) {
            $products_submenu[] = [
                'name'     => 'Експорт',
                'href'     => $this->getAdminLink('catalog/product/export'),
                'active'   => $this->isActiveRoute('catalog/product/export')
            ];
        }

        if ($this->hasPermission('access', 'catalog/product')) {
            $products_submenu[] = [
                'name'     => 'Промоции',
                'href'     => $this->getAdminLink('catalog/product/promotion'),
                'active'   => $this->isActiveRoute('catalog/product/promotion')
            ];
        }

        if ($this->hasPermission('access', 'catalog/attribute')) {
            $products_submenu[] = [
                'name'     => 'Атрибути',
                'href'     => $this->getAdminLink('catalog/attribute'),
                'active'   => $this->isActiveMainRoute('catalog/attribute')
            ];
        }

        if ($this->hasPermission('access', 'catalog/option')) {
            $products_submenu[] = [
                'name'     => 'Опции',
                'href'     => $this->getAdminLink('catalog/option'),
                'active'   => $this->isActiveMainRoute('catalog/option')
            ];
        }

        if ($this->hasPermission('access', 'catalog/manufacturer')) {
            $products_submenu[] = [
                'name'     => 'Производители',
                'href'     => $this->getAdminLink('catalog/manufacturer'),
                'active'   => $this->isActiveMainRoute('catalog/manufacturer')
            ];
        }

        if ($this->hasPermission('access', 'catalog/review')) {
            $products_submenu[] = [
                'name'     => 'Клиентски мнения',
                'href'     => $this->getAdminLink('catalog/review'),
                'active'   => $this->isActiveMainRoute('catalog/review')
            ];
        }



        // Добавяне на главното меню с субменютата, ако има поне едно субменю
        if (!empty($products_submenu)) {
            $menus[] = [
                'id'       => 'menu-products',
                'icon'     => 'ri-shopping-bag-line',
                'name'     => 'Продукти',
                'href'     => '', // Празен href за главното меню със субменюта
                'active'   => $this->isActiveMainRoute('catalog/product'),
                'children' => $products_submenu
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за категории
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addCategoriesMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'catalog/category')) {
            $menus[] = [
                'id'       => 'menu-categories',
                'icon'     => 'ri-price-tag-3-line',
                'name'     => 'Категории',
                'href'     => $this->getAdminLink('catalog/category'),
                'active'   => $this->isActiveMainRoute('catalog/category')
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за клиенти
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addCustomersMenu(&$menus) {
        // Създаване на масив за субменютата
        $customers_submenu = [];

        // Добавяне на субменютата с проверка за права на достъп
        if ($this->hasPermission('access', 'customer/customer')) {
            $customers_submenu[] = [
                'name'     => 'Клиенти',
                'href'     => $this->getAdminLink('customer/customer'),
                'active'   => $this->isActiveRoute('customer/customer')
            ];
        }

        if ($this->hasPermission('access', 'customer/customer_group')) {
            $customers_submenu[] = [
                'name'     => 'Клиентски групи',
                'href'     => $this->getAdminLink('customer/customer_group'),
                'active'   => $this->isActiveRoute('customer/customer_group')
            ];
        }

        if ($this->hasPermission('access', 'customer/customer_approval')) {
            $customers_submenu[] = [
                'name'     => 'Одобрения',
                'href'     => $this->getAdminLink('customer/customer_approval'),
                'active'   => $this->isActiveRoute('customer/customer_approval')
            ];
        }

        if ($this->hasPermission('access', 'customer/custom_field')) {
            $customers_submenu[] = [
                'name'     => 'Персонализирани полета',
                'href'     => $this->getAdminLink('customer/custom_field'),
                'active'   => $this->isActiveRoute('customer/custom_field')
            ];
        }

        if ($this->hasPermission('access', 'customer/fakedetector')) {
            $customers_submenu[] = [
                'name'     => 'Fake Detector',
                'href'     => $this->getAdminLink('customer/fakedetector'),
                'active'   => $this->isActiveRoute('customer/fakedetector')
            ];
        }

        // Добавяне на главното меню с субменютата, ако има поне едно субменю
        if (!empty($customers_submenu)) {
            $menus[] = [
                'id'       => 'menu-customers',
                'icon'     => 'ri-user-line',
                'name'     => 'Клиенти',
                'href'     => '', // Празен href за главното меню със субменюта
                'active'   => $this->isActiveMainRoute('customer/'),
                'children' => $customers_submenu
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за поръчки
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addOrdersMenu(&$menus) {


        // Създаване на масив за субменютата
        $submenu = [];

        // Добавяне на субменютата с проверка за права на достъп
        if ($this->hasPermission('access', 'sale/order')) {
            $submenu[] = [
                'name'     => 'Поръчки',
                'href'     => $this->getAdminLink('sale/order'),
                'active'   => $this->isActiveRoute('sale/order')
            ];

            $submenu[] = [
                'name'     => 'Бързи поръчки',
                'href'     => $this->getAdminLink('sale/quick_order'),
                'active'   => $this->isActiveRoute('sale/quick_order')
            ];

            $submenu[] = [
                'name'     => 'Изоставени колички',
                'href'     => $this->getAdminLink('sale/abandoned_cart'),
                'active'   => $this->isActiveRoute('sale/abandoned_cart')
            ];
        }



        // Проверка за права на достъп
        if ($this->hasPermission('access', 'sale/order')) {
            $menus[] = [
                'id'       => 'menu-orders',
                'icon'     => 'ri-shopping-cart-line',
                'name'     => 'Поръчки',
                'href'     => $this->getAdminLink('sale/order'),
                'active'   => $this->isActiveMainRoute('sale/'),
                'children' => $submenu
            ];
        }

        return $this;
    }

    
    /**
     * Добавя менюто за Запитвания
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addRequestsMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'sale/request')) {
            $menus[] = [
                'id'       => 'menu-orders',
                'icon'     => 'ri-questionnaire-line',
                'name'     => 'Запитвания',
                'href'     => $this->getAdminLink('sale/request'),
                'active'   => $this->isActiveMainRoute('sale/request')
            ];
        }

        return $this;
    }



    /**
     * Добавя менюто за настройки
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addSettingsMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'setting/setting')) {
            $menus[] = [
                'id'       => 'menu-settings',
                'icon'     => 'ri-settings-line',
                'name'     => 'Настройки',
                'href'     => $this->getAdminLink('setting/setting'),
                'active'   => $this->isActiveMainRoute('setting/setting')
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за отчети
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addReportsMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'report/report')) {
            $menus[] = [
                'id'       => 'menu-reports',
                'icon'     => 'ri-bar-chart-line',
                'name'     => 'Отчети',
                'href'     => $this->getAdminLink('report/report'),
                'active'   => $this->isActiveMainRoute('report/report')
            ];
        }

        return $this;
    }

    /**
     * Добавя менюто за маркетинг
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addMarketingMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'marketing/marketing')) {
            $menus[] = [
                'id'       => 'menu-marketing',
                'icon'     => 'ri-global-line',
                'name'     => 'Маркетинг',
                'href'     => $this->getAdminLink('marketing/marketing'),
                'active'   => $this->isActiveMainRoute('marketing/marketing')
            ];
        }

        return $this;
    }


    /**
     * Добавя менюто за темата
     *
     * @param array $menus Масив с менюта
     * @return $this За верижно извикване на методи
     */
    private function addThemeMenu(&$menus) {
        // Проверка за права на достъп
        if ($this->hasPermission('access', 'system/theme')) {
            $menus[] = [
                'id'       => 'menu-theme',
                'icon'     => 'ri-palette-line',
                'name'     => 'Тема',
                'href'     => $this->getAdminLink('system/theme'),
                'active'   => $this->isActiveMainRoute('system/theme')
            ];
        }

        return $this;
    }



    /**
     * Проверява дали текущият маршрут е активен
     *
     * @param string $route Маршрут за проверка
     * @return boolean Връща true, ако маршрутът е активен
     */
    private function isActiveRoute($route) {
        $current_route = isset($this->request->get['route']) ? $this->request->get['route'] : '';
        return ($current_route === $route);
    }

    /**
     * Проверява дали текущият главен маршрут е активен
     *
     * @param string $route Маршрут за проверка
     * @return boolean Връща true, ако маршрутът е активен
     */
    private function isActiveMainRoute($route) {
        $current_route = isset($this->request->get['route']) ? $this->request->get['route'] : '';
        return (strpos($current_route, $route) === 0);
    }


}

<?php

namespace Theme25\Backend\Controller\Sale;

class Feedback extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/feedback');
    }

    public function index() {
        $this->setTitle('Клиентски мнения');

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        $this->prepareReviewListData()
             ->prepareFilterOptions()
             ->prepareReviewItems()
             ->preparePagination();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('sale/feedback');
    }

    /**
     * Подготвя основните данни за списъка с отзиви
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareReviewListData() {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders',
            'catalog/product' => 'products',
            'customer/customer' => 'customers'
        ]);

        // URL адреси - използваме getAdminLinks за групово извличане
        $routes = [
            'add_new_url' => 'sale/feedback/add',
            'delete_url' => 'sale/feedback/delete&order_id=ORDER_ID',
            'edit_url' => 'sale/feedback/edit&order_id=ORDER_ID'
        ];

        // Добавяне на URL адресите към данните
        $this->setData($this->getAdminLinks($routes));

        return $this;
    }

    /**
     * Подготвя опциите за филтриране и сортиране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Получаване на данните за филтрите
        $filter_data = $this->getFilterData();

        // Опции за сортиране
        $sort_options = [
            ['value' => 'r.date_added-DESC', 'text' => 'Последно добавени'],
            ['value' => 'r.date_added-ASC', 'text' => 'Първо най-стари'],
            ['value' => 'r.rating-DESC', 'text' => 'Оценка (висока към ниска)'],
            ['value' => 'r.rating-ASC', 'text' => 'Оценка (ниска към висока)'],
            ['value' => 'r.feedback-DESC', 'text' => 'Препоръчани']
        ];

        // URL адреси за филтри - използваме getAdminLinks за групово извличане
        $filter_urls = $this->getAdminLinks([
            'sort_url' => 'sale/feedback',
            'limit_url' => 'sale/feedback',
            'filter_feedback_url' => 'sale/feedback'
        ], [
            'sort_url' => '&sort=SORT_VALUE',
            'limit_url' => '&limit=LIMIT_VALUE',
            'filter_feedback_url' => '&filter_feedback=FILTER_VALUE'
        ]);

        // Текущи филтри
        $current_filters = [
            'filter_feedback' => isset($filter_data['filter_feedback']) && $filter_data['filter_feedback'] == 1,
            'view_type' => $this->requestGet('view') ?: 'grid',
            'limit' => $filter_data['limit']
        ];

        // Добавяне на данните към $this->data с един метод
        $this->setData([
            'sort_options' => $sort_options,
            'filter_data' => $filter_data
        ])
        ->setData($filter_urls)
        ->setData($current_filters);

        return $this;
    }

    /**
     * Подготвя списъка с отзиви
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareReviewItems() {
        // Получаване на данните за филтрите
        $filter_data = $this->data['filter_data'];

        // Получаване на отзивите
        $results = $this->reviews->getReviews($filter_data);

        // Подготовка на данните за отзивите
        $reviews = [];

        foreach ($results as $result) {
            // Получаване на информация за клиента
            $customer_info = $this->customers->getCustomer($result['customer_id']);
            $customer_name = $customer_info ? $customer_info['firstname'] . ' ' . $customer_info['lastname'] : 'Неизвестен';
            $customer_email = $customer_info ? $customer_info['email'] : '';

            // Получаване на информация за поръчката
            $order_id = isset($result['order_id']) ? $result['order_id'] : 0;
            $order_number = $order_id ? '#RK-' . sprintf('%08d', $order_id) : 'Няма данни';

            // Преобразуване на препоръката в текст
            $feedback_text = '';
            if (isset($result['feedback'])) {
                if ($result['feedback'] == 1) {
                    $feedback_text = 'Да';
                } elseif ($result['feedback'] == 0) {
                    $feedback_text = 'Не';
                } else {
                    $feedback_text = 'Може би';
                }
            }

            // Подготовка на данните за отзива
            $reviews[] = [
                'review_id' => $result['review_id'],
                'order_id' => $order_id,
                'order_number' => $order_number,
                'customer_name' => $customer_name,
                'customer_email' => $customer_email,
                'rating' => $result['rating'],
                'feedback' => isset($result['feedback']) ? $result['feedback'] : null,
                'feedback_text' => $feedback_text,
                'date_added' => date('d M Y', strtotime($result['date_added'])),
                'text' => $result['text'],
                'edit' => str_replace('REVIEW_ID', $result['review_id'], $this->data['edit_url']),
                'delete' => str_replace('REVIEW_ID', $result['review_id'], $this->data['delete_url'])
            ];
        }

        // Добавяне на отзивите към данните
        $this->setData('reviews', $reviews);

        return $this;
    }

    /**
     * Подготвя пагинацията
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePagination() {
        // Получаване на данните за филтрите
        $filter_data = $this->data['filter_data'];

        // Получаване на общия брой отзиви
        $review_total = $this->reviews->getTotalReviews($filter_data);

        // Опции за брой отзиви на страница
        $limits = [
            ['value' => 10, 'text' => '10 на страница'],
            ['value' => 20, 'text' => '20 на страница'],
            ['value' => 50, 'text' => '50 на страница'],
            ['value' => 100, 'text' => '100 на страница']
        ];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $review_total;
        $pagination->page = $filter_data['page'];
        $pagination->limit = $filter_data['limit'];
        $pagination->url = $this->getAdminLink('catalog/review', '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('catalog/review', '&limit={limit}'));
        $pagination->setProductText('отзива');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Получава данните за филтрите от заявката
     *
     * @return array Масив с данни за филтрите
     */
    private function getFilterData() {
        // Филтри
        $filter_data = [];

        // Сортиране
        $sort = $this->requestGet('sort') ?: 'r.date_added';
        $order = $this->requestGet('order') ?: 'DESC';

        // Ако сортирането е във формат 'field-ORDER'
        if (strpos($sort, '-') !== false) {
            list($sort, $order) = explode('-', $sort);
        }

        $filter_data['sort'] = $sort;
        $filter_data['order'] = $order;

        // Пагинация
        $page = (int)$this->requestGet('page') ?: 1;
        $limit = (int)$this->requestGet('limit') ? $this->requestGet('limit') : 20;

        $filter_data['start'] = ($page - 1) * $limit;
        $filter_data['limit'] = $limit;
        $filter_data['page'] = $page;

        // Филтри
        $filter_fields = [
            'filter_customer',
            'filter_product',
            'filter_rating',
            'filter_feedback',
            'filter_date_added_start',
            'filter_date_added_end'
        ];

        foreach ($filter_fields as $field) {
            if ($value = $this->requestGet($field)) {
                $filter_data[$field] = $value;
            }
        }

        return $filter_data;
    }
}
